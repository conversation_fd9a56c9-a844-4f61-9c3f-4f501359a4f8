<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MongoDB Item Manager</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .container {
            display: flex;
            gap: 30px;
        }
        .form-section, .items-section {
            flex: 1;
        }
        form {
            background-color: #f5f5f5;
            padding: 20px;
            border-radius: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .item-card {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .item-card h3 {
            margin-top: 0;
            color: #333;
        }
        .item-property {
            margin-bottom: 5px;
        }
        .item-property span {
            font-weight: bold;
        }
        .error {
            color: red;
            margin-top: 10px;
        }
        .success {
            color: green;
            margin-top: 10px;
        }
        #itemDetails {
            background-color: #e9f7ef;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            display: none;
        }
    </style>
</head>
<body>
    <h1>MongoDB Item Manager</h1>

    <div class="container">
        <div class="form-section">
            <h2>Add New Item</h2>
            <form id="addItemForm">
                <div class="form-group">
                    <label for="name">Name:</label>
                    <input type="text" id="name" name="name" required>
                </div>
                <div class="form-group">
                    <label for="description">Description:</label>
                    <textarea id="description" name="description" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label for="price">Price:</label>
                    <input type="number" id="price" name="price" step="0.01" min="0" required>
                </div>
                <div class="form-group">
                    <label for="quantity">Quantity:</label>
                    <input type="number" id="quantity" name="quantity" min="0" required>
                </div>
                <button type="submit">Add Item</button>
                <div id="addItemMessage"></div>
            </form>

            <h2>Find Item by ID</h2>
            <form id="findItemForm">
                <div class="form-group">
                    <label for="itemId">Item ID:</label>
                    <input type="text" id="itemId" name="itemId" required>
                </div>
                <button type="submit">Find Item</button>
            </form>
            <div id="itemDetails"></div>
        </div>

        <div class="items-section">
            <h2>All Items</h2>
            <button id="refreshItems">Refresh Items</button>
            <div id="itemsList"></div>
        </div>
    </div>

    <script>
        // API base URL - using the same server that serves this HTML
        const API_URL = window.location.origin;

        // Function to add a new item
        document.getElementById('addItemForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = {
                name: document.getElementById('name').value,
                description: document.getElementById('description').value || null,
                price: parseFloat(document.getElementById('price').value),
                quantity: parseInt(document.getElementById('quantity').value)
            };

            try {
                const response = await fetch(`${API_URL}/items/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('addItemMessage').innerHTML =
                        `<div class="success">Item added successfully with ID: ${data._id}</div>`;
                    document.getElementById('addItemForm').reset();
                    loadItems(); // Refresh the items list
                } else {
                    const error = await response.json();
                    document.getElementById('addItemMessage').innerHTML =
                        `<div class="error">Error: ${error.detail || 'Failed to add item'}</div>`;
                }
            } catch (error) {
                document.getElementById('addItemMessage').innerHTML =
                    `<div class="error">Error: ${error.message}</div>`;
            }
        });

        // Function to load all items
        async function loadItems() {
            try {
                const response = await fetch(`${API_URL}/items/`);
                if (response.ok) {
                    const items = await response.json();
                    const itemsList = document.getElementById('itemsList');

                    if (items.length === 0) {
                        itemsList.innerHTML = '<p>No items found.</p>';
                        return;
                    }

                    let html = '';
                    items.forEach(item => {
                        html += `
                            <div class="item-card">
                                <h3>${item.name}</h3>
                                <div class="item-property"><span>ID:</span> ${item._id}</div>
                                ${item.description ? `<div class="item-property"><span>Description:</span> ${item.description}</div>` : ''}
                                <div class="item-property"><span>Price:</span> $${item.price.toFixed(2)}</div>
                                <div class="item-property"><span>Quantity:</span> ${item.quantity}</div>
                            </div>
                        `;
                    });

                    itemsList.innerHTML = html;
                } else {
                    document.getElementById('itemsList').innerHTML =
                        `<div class="error">Error loading items</div>`;
                }
            } catch (error) {
                document.getElementById('itemsList').innerHTML =
                    `<div class="error">Error: ${error.message}</div>`;
            }
        }

        // Function to find an item by ID
        document.getElementById('findItemForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const itemId = document.getElementById('itemId').value;
            const itemDetails = document.getElementById('itemDetails');

            try {
                const response = await fetch(`${API_URL}/items/${itemId}`);

                if (response.ok) {
                    const item = await response.json();
                    itemDetails.style.display = 'block';
                    itemDetails.innerHTML = `
                        <h3>${item.name}</h3>
                        <div class="item-property"><span>ID:</span> ${item._id}</div>
                        ${item.description ? `<div class="item-property"><span>Description:</span> ${item.description}</div>` : ''}
                        <div class="item-property"><span>Price:</span> $${item.price.toFixed(2)}</div>
                        <div class="item-property"><span>Quantity:</span> ${item.quantity}</div>
                    `;
                } else {
                    const error = await response.json();
                    itemDetails.style.display = 'block';
                    itemDetails.innerHTML = `<div class="error">Error: ${error.detail || 'Item not found'}</div>`;
                }
            } catch (error) {
                itemDetails.style.display = 'block';
                itemDetails.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        });

        // Refresh items button
        document.getElementById('refreshItems').addEventListener('click', loadItems);

        // Load items when the page loads
        document.addEventListener('DOMContentLoaded', loadItems);
    </script>
</body>
</html>
