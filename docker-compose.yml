version: '3.8'

services:
  # MongoDB service
  mongodb:
    image: mongo:latest
    container_name: mongodb
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - app-network
    # Uncomment the following lines if you want to set up authentication
    # environment:
    #   MONGO_INITDB_ROOT_USERNAME: admin
    #   MONGO_INITDB_ROOT_PASSWORD: password

  # FastAPI application
  fastapi:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: fastapi-app
    ports:
      - "8000:8000"
    depends_on:
      - mongodb
    environment:
      - MONGO_HOST=mongodb
      - MONGO_PORT=27017
      - MONGO_DB=simple_db
      - MONGO_COLLECTION=items
      # Uncomment the following lines if you set up authentication in MongoDB
      # - MONGO_USERNAME=admin
      # - MONGO_PASSWORD=password
    networks:
      - app-network
    volumes:
      - .:/app

networks:
  app-network:
    driver: bridge

volumes:
  mongodb_data:
