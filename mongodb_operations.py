"""
MongoDB Operations Script

This script demonstrates basic CRUD operations with MongoDB using PyMongo.
It includes functions to connect to a database and perform various operations.
"""

import os
from pymongo import MongoClient
from dotenv import load_dotenv
from pprint import pprint
import datetime

# Load environment variables from .env file (if it exists)
load_dotenv()

# MongoDB connection string - using environment variable or default to localhost
MONGO_URI = os.getenv("MONGO_URI", "mongodb://localhost:27017/")

def connect_to_mongodb():
    """Connect to MongoDB and return client"""
    try:
        # Create a connection using MongoClient
        client = MongoClient(MONGO_URI)
        
        # Test the connection
        client.admin.command('ping')
        print("Connected successfully to MongoDB!")
        return client
    except Exception as e:
        print(f"Could not connect to MongoDB: {e}")
        return None

def create_document(collection, document):
    """Insert a document into the specified collection"""
    try:
        result = collection.insert_one(document)
        print(f"Document inserted with ID: {result.inserted_id}")
        return result.inserted_id
    except Exception as e:
        print(f"Error inserting document: {e}")
        return None

def create_multiple_documents(collection, documents):
    """Insert multiple documents into the specified collection"""
    try:
        result = collection.insert_many(documents)
        print(f"Documents inserted with IDs: {result.inserted_ids}")
        return result.inserted_ids
    except Exception as e:
        print(f"Error inserting documents: {e}")
        return None

def find_document(collection, query):
    """Find a document in the collection based on the query"""
    try:
        document = collection.find_one(query)
        if document:
            print("Document found:")
            pprint(document)
        else:
            print("No document matches the query")
        return document
    except Exception as e:
        print(f"Error finding document: {e}")
        return None

def find_all_documents(collection, query=None, projection=None):
    """Find all documents matching the query with optional projection"""
    try:
        documents = collection.find(query or {}, projection or {})
        count = collection.count_documents(query or {})
        print(f"Found {count} documents:")
        for doc in documents:
            pprint(doc)
        return documents
    except Exception as e:
        print(f"Error finding documents: {e}")
        return None

def update_document(collection, query, update_data):
    """Update a document in the collection"""
    try:
        result = collection.update_one(query, {"$set": update_data})
        print(f"Modified {result.modified_count} document(s)")
        return result.modified_count
    except Exception as e:
        print(f"Error updating document: {e}")
        return None

def delete_document(collection, query):
    """Delete a document from the collection"""
    try:
        result = collection.delete_one(query)
        print(f"Deleted {result.deleted_count} document(s)")
        return result.deleted_count
    except Exception as e:
        print(f"Error deleting document: {e}")
        return None

def main():
    """Main function to demonstrate MongoDB operations"""
    # Connect to MongoDB
    client = connect_to_mongodb()
    if not client:
        return
    
    # Access a database (will be created if it doesn't exist)
    db_name = "sample_database"
    db = client[db_name]
    print(f"Using database: {db_name}")
    
    # Access a collection (will be created if it doesn't exist)
    collection_name = "users"
    collection = db[collection_name]
    print(f"Using collection: {collection_name}")
    
    # Create a sample document
    user = {
        "name": "John Doe",
        "email": "<EMAIL>",
        "age": 30,
        "address": {
            "street": "123 Main St",
            "city": "Anytown",
            "state": "CA",
            "zip": "12345"
        },
        "interests": ["reading", "hiking", "photography"],
        "created_at": datetime.datetime.now()
    }
    
    # Insert the document
    user_id = create_document(collection, user)
    
    # Create multiple documents
    additional_users = [
        {
            "name": "Jane Smith",
            "email": "<EMAIL>",
            "age": 28,
            "address": {
                "street": "456 Oak Ave",
                "city": "Somewhere",
                "state": "NY",
                "zip": "67890"
            },
            "interests": ["painting", "traveling", "cooking"],
            "created_at": datetime.datetime.now()
        },
        {
            "name": "Bob Johnson",
            "email": "<EMAIL>",
            "age": 35,
            "address": {
                "street": "789 Pine Rd",
                "city": "Elsewhere",
                "state": "TX",
                "zip": "54321"
            },
            "interests": ["gaming", "movies", "sports"],
            "created_at": datetime.datetime.now()
        }
    ]
    create_multiple_documents(collection, additional_users)
    
    # Find a document
    print("\nFinding a document by name:")
    find_document(collection, {"name": "John Doe"})
    
    # Find all documents
    print("\nFinding all documents:")
    find_all_documents(collection)
    
    # Find with projection (only return specific fields)
    print("\nFinding documents with projection (name and email only):")
    find_all_documents(collection, {}, {"name": 1, "email": 1, "_id": 0})
    
    # Update a document
    print("\nUpdating John Doe's age:")
    update_document(collection, {"name": "John Doe"}, {"age": 31, "updated_at": datetime.datetime.now()})
    
    # Verify the update
    print("\nVerifying the update:")
    find_document(collection, {"name": "John Doe"})
    
    # Delete a document
    print("\nDeleting Bob Johnson's document:")
    delete_document(collection, {"name": "Bob Johnson"})
    
    # Verify the deletion by counting remaining documents
    print("\nVerifying deletion by counting remaining documents:")
    count = collection.count_documents({})
    print(f"Remaining documents: {count}")
    
    # Close the connection
    client.close()
    print("\nMongoDB connection closed")

if __name__ == "__main__":
    main()
