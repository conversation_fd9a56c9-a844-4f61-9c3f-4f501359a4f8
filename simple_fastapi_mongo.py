"""
Minimal FastAPI app with MongoDB connection
"""
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from pymongo import MongoClient
from pydantic import BaseModel
from typing import Optional
import os
from dotenv import load_dotenv

# Create FastAPI app
app = FastAPI()

# Add CORS middleware to allow our HTML page to communicate with the API
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Load environment variables
load_dotenv()

# MongoDB connection settings
MONGO_HOST = os.getenv("MONGO_HOST", "localhost")
MONGO_PORT = os.getenv("MONGO_PORT", "27017")
MONGO_DB = os.getenv("MONGO_DB", "simple_db")
MONGO_USERNAME = os.getenv("MONGO_USERNAME")
MONGO_PASSWORD = os.getenv("MONGO_PASSWORD")

# MongoDB connection string
if MONGO_USERNAME and MONGO_PASSWORD:
    mongo_uri = f"mongodb://{MONGO_USERNAME}:{MONGO_PASSWORD}@{MONGO_HOST}:{MONGO_PORT}/"
    print(f"Connecting to MongoDB at: {MONGO_HOST}:{MONGO_PORT} with authentication")
else:
    mongo_uri = f"mongodb://{MONGO_HOST}:{MONGO_PORT}/"
    print(f"Connecting to MongoDB at: {MONGO_HOST}:{MONGO_PORT} without authentication")

# Connect to MongoDB
try:
    client = MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
    # Verify connection
    client.admin.command('ping')
    print("Successfully connected to MongoDB!")
    db = client[MONGO_DB]
    collection = db["items"]
except Exception as e:
    print(f"Failed to connect to MongoDB: {e}")
    # We'll still define these variables, but the app will show an error when trying to use them
    db = None
    collection = None

# Data model
class Item(BaseModel):
    name: str
    description: Optional[str] = None
    price: float
    quantity: int

# API endpoints
@app.get("/", response_class=HTMLResponse)
def read_root():
    with open("index.html", "r") as f:
        html_content = f.read()
    return html_content

@app.get("/api")
def read_api_root():
    return {"message": "Simple FastAPI MongoDB API"}

@app.post("/items/")
def create_item(item: Item):
    if collection is None:
        raise HTTPException(status_code=503, detail="Database connection is not available")

    try:
        item_dict = item.model_dump()
        result = collection.insert_one(item_dict)
        item_dict["_id"] = str(result.inserted_id)
        return item_dict
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@app.get("/items/")
def read_items():
    if collection is None:
        raise HTTPException(status_code=503, detail="Database connection is not available")

    try:
        items = list(collection.find())
        for item in items:
            item["_id"] = str(item["_id"])
        return items
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@app.get("/items/{item_id}")
def read_item(item_id: str):
    if collection is None:
        raise HTTPException(status_code=503, detail="Database connection is not available")

    from bson.objectid import ObjectId

    try:
        if not ObjectId.is_valid(item_id):
            raise HTTPException(status_code=400, detail="Invalid ID format")

        item = collection.find_one({"_id": ObjectId(item_id)})
        if item:
            item["_id"] = str(item["_id"])
            return item
        raise HTTPException(status_code=404, detail="Item not found")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("simple_fastapi_mongo:app", host="0.0.0.0", port=8000, reload=True)
