"""
Minimal FastAPI app with MongoDB connection
"""
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from pymongo import Mongo<PERSON>lient
from pydantic import BaseModel
from typing import Optional

# Create FastAPI app
app = FastAPI()

# Add CORS middleware to allow our HTML page to communicate with the API
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# MongoDB connection
client = MongoClient("mongodb://localhost:27017/")
db = client["simple_db"]
collection = db["items"]

# Data model
class Item(BaseModel):
    name: str
    description: Optional[str] = None
    price: float
    quantity: int

# API endpoints
@app.get("/", response_class=HTMLResponse)
def read_root():
    with open("index.html", "r") as f:
        html_content = f.read()
    return html_content

@app.get("/api")
def read_api_root():
    return {"message": "Simple FastAPI MongoDB API"}

@app.post("/items/")
def create_item(item: Item):
    item_dict = item.model_dump()
    result = collection.insert_one(item_dict)
    item_dict["_id"] = str(result.inserted_id)
    return item_dict

@app.get("/items/")
def read_items():
    items = list(collection.find())
    for item in items:
        item["_id"] = str(item["_id"])
    return items

@app.get("/items/{item_id}")
def read_item(item_id: str):
    from bson.objectid import ObjectId

    try:
        item = collection.find_one({"_id": ObjectId(item_id)})
        if item:
            item["_id"] = str(item["_id"])
            return item
        raise HTTPException(status_code=404, detail="Item not found")
    except:
        raise HTTPException(status_code=400, detail="Invalid ID format")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("simple_fastapi_mongo:app", host="0.0.0.0", port=8000, reload=True)
