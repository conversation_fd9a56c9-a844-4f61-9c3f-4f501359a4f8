"""
Simple script to test MongoDB connection
"""
from pymongo import MongoClient
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Get MongoDB connection details from environment variables
MONGO_HOST = os.getenv("MONGO_HOST", "localhost")
MONGO_PORT = os.getenv("MONGO_PORT", "27017")
MONGO_USERNAME = os.getenv("MONGO_USERNAME", "")
MONGO_PASSWORD = os.getenv("MONGO_PASSWORD", "")

# Build connection string
if MONGO_USERNAME and MONGO_PASSWORD:
    mongo_uri = f"mongodb://{MONGO_USERNAME}:{MONGO_PASSWORD}@{MONGO_HOST}:{MONGO_PORT}/"
else:
    # Try without authentication first
    mongo_uri = f"mongodb://{MONGO_HOST}:{MONGO_PORT}/"
print(f"Attempting to connect to MongoDB at: {mongo_uri}")

try:
    # Try to connect with a short timeout
    client = MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)

    # Force a connection by requesting server info
    server_info = client.server_info()

    print("Successfully connected to MongoDB!")
    print(f"MongoDB version: {server_info.get('version', 'unknown')}")

    # List available databases
    print("\nAvailable databases:")
    for db_name in client.list_database_names():
        print(f"- {db_name}")

    client.close()

except Exception as e:
    print(f"Failed to connect to MongoDB: {e}")
    print("\nPossible reasons:")
    print("1. MongoDB is not running")
    print("2. MongoDB is not accessible at the specified host/port")
    print("3. MongoDB is running in a Docker container with no port mapping")
    print("4. A firewall is blocking the connection")

    print("\nSuggestions:")
    print("- Check if your MongoDB Docker container is running")
    print("- Verify the port mapping in your Docker container")
    print("- Update the MONGO_HOST and MONGO_PORT in your .env file if needed")
