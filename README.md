# FastAPI MongoDB CRUD Application

A simple FastAPI application that connects to MongoDB and provides CRUD operations for a collection of items.

## Features

- Connect to MongoDB using PyMongo
- Create, Read, Update, and Delete operations for items
- Input validation using Pydantic models
- Proper error handling
- API documentation with Swagger UI

## Requirements

- Python 3.7+
- MongoDB (running locally or remotely)
- FastAPI
- Uvicorn
- PyMongo
- python-dotenv

## Installation

1. Clone the repository
2. Create a virtual environment:
   ```
   python -m venv venv
   ```
3. Activate the virtual environment:
   - Windows: `venv\Scripts\activate`
   - Linux/Mac: `source venv/bin/activate`
4. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
5. Configure MongoDB connection in `.env` file:
   ```
   MONGO_URI=mongodb://localhost:27017/
   MONGO_DB=fastapi_db
   ```

## Running the Application

Run the application with:

```
python run.py
```

Or directly with uvicorn:

```
uvicorn main:app --reload
```

The API will be available at http://localhost:8000

## API Documentation

Once the application is running, you can access the API documentation at:

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## API Endpoints

- `GET /`: Root endpoint
- `POST /items/`: Create a new item
- `GET /items/`: Get all items
- `GET /items/{item_id}`: Get a specific item by ID
- `PUT /items/{item_id}`: Update an item
- `DELETE /items/{item_id}`: Delete an item

## Data Model

The Item model includes:

- `name`: String (required)
- `description`: String (optional)
- `price`: Float (required)
- `quantity`: Integer (required)
- `tags`: List of strings (optional)
- `created_at`: Datetime (automatically set)
- `updated_at`: Datetime (set on updates)
